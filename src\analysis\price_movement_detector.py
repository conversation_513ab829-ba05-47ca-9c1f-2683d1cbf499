#!/usr/bin/env python3
"""
Price Movement Detection Algorithm
Detects 5% down-moves that occur within ≤120 seconds
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from datetime import timedelta
import sys
import os

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.data_processor import BinanceDataProcessor

class PriceMovementDetector:
    """Detect significant price movements in high-frequency data"""
    
    def __init__(self, down_move_threshold: float = 0.05, max_duration_seconds: int = 120):
        """
        Initialize the detector
        
        Args:
            down_move_threshold: Minimum percentage drop to detect (0.05 = 5%)
            max_duration_seconds: Maximum time window for the move (120 seconds)
        """
        self.down_move_threshold = down_move_threshold
        self.max_duration_seconds = max_duration_seconds
        self.processor = BinanceDataProcessor()
    
    def detect_down_moves_from_trades(self, trades_df: pd.DataFrame) -> List[Dict]:
        """
        Detect 5% down-moves from trades data using efficient vectorized approach

        Args:
            trades_df: DataFrame with trades data

        Returns:
            List of detected down-moves with metadata
        """
        down_moves = []

        # Sort by time to ensure chronological order
        trades_df = trades_df.sort_values('time').reset_index(drop=True)

        print(f"Analyzing {len(trades_df)} trades for down-moves...")

        # Use klines data for more efficient processing (resample trades to 1s intervals)
        trades_df['timestamp_s'] = trades_df['time'].dt.floor('1s')

        # Group by second and get OHLC
        ohlc_data = trades_df.groupby('timestamp_s')['price'].agg(['first', 'max', 'min', 'last']).reset_index()
        ohlc_data.columns = ['time', 'open', 'high', 'low', 'close']

        print(f"Resampled to {len(ohlc_data)} 1-second intervals")

        # Now use the more efficient klines-based detection
        return self._detect_from_ohlc_data(ohlc_data)

    def _detect_from_ohlc_data(self, ohlc_df: pd.DataFrame) -> List[Dict]:
        """
        Efficient detection using OHLC data
        """
        down_moves = []

        # Convert to numpy arrays for faster processing
        times = ohlc_df['time'].values
        opens = ohlc_df['open'].values
        lows = ohlc_df['low'].values

        max_window_size = self.max_duration_seconds  # 120 seconds = 120 intervals

        for i in range(len(ohlc_df) - 1):
            start_time = times[i]
            start_price = opens[i]

            # Define the window end
            end_idx = min(i + max_window_size, len(ohlc_df))

            # Find minimum price in the window
            window_lows = lows[i:end_idx]
            min_price = np.min(window_lows)
            min_idx = i + np.argmin(window_lows)

            # Calculate price drop
            price_drop = (start_price - min_price) / start_price

            if price_drop >= self.down_move_threshold:
                end_time = times[min_idx]
                duration = (pd.Timestamp(end_time) - pd.Timestamp(start_time)).total_seconds()

                down_move = {
                    'start_time': pd.Timestamp(start_time),
                    'end_time': pd.Timestamp(end_time),
                    'duration_seconds': duration,
                    'start_price': start_price,
                    'min_price': min_price,
                    'price_drop_pct': price_drop * 100
                }

                down_moves.append(down_move)

                print(f"Down-move detected: {price_drop*100:.2f}% drop in {duration:.1f}s "
                      f"from {start_price:.2f} to {min_price:.2f}")

                # Skip ahead to avoid overlapping detections
                i = min_idx

        print(f"Total down-moves detected: {len(down_moves)}")
        return down_moves
    
    def detect_down_moves_from_klines(self, klines_df: pd.DataFrame) -> List[Dict]:
        """
        Detect 5% down-moves from 1-second klines data
        
        Args:
            klines_df: DataFrame with klines data
            
        Returns:
            List of detected down-moves with metadata
        """
        down_moves = []
        
        # Sort by time to ensure chronological order
        klines_df = klines_df.sort_values('open_time').reset_index(drop=True)
        
        print(f"Analyzing {len(klines_df)} klines for down-moves...")
        
        # Use a sliding window approach
        i = 0
        while i < len(klines_df) - 1:
            start_time = klines_df.iloc[i]['open_time']
            start_price = klines_df.iloc[i]['close']  # Use close price
            
            # Look ahead within the time window
            max_time = start_time + timedelta(seconds=self.max_duration_seconds)
            
            # Find all klines within the time window
            window_mask = (klines_df['open_time'] >= start_time) & (klines_df['open_time'] <= max_time)
            window_klines = klines_df[window_mask].copy()
            
            if len(window_klines) < 2:
                i += 1
                continue
            
            # Find the minimum low price in this window
            min_price_idx = window_klines['low'].idxmin()
            min_price = window_klines.loc[min_price_idx, 'low']
            min_time = window_klines.loc[min_price_idx, 'open_time']
            
            # Calculate the percentage drop
            price_drop = (start_price - min_price) / start_price
            
            # Check if this qualifies as a down-move
            if price_drop >= self.down_move_threshold:
                duration = (min_time - start_time).total_seconds()
                
                down_move = {
                    'start_time': start_time,
                    'end_time': min_time,
                    'duration_seconds': duration,
                    'start_price': start_price,
                    'min_price': min_price,
                    'price_drop_pct': price_drop * 100,
                    'start_volume': klines_df.iloc[i]['volume'],
                    'total_volume': window_klines['volume'].sum()
                }
                
                down_moves.append(down_move)
                
                print(f"Down-move detected: {price_drop*100:.2f}% drop in {duration:.1f}s "
                      f"from {start_price:.2f} to {min_price:.2f}")
                
                # Skip ahead to avoid overlapping detections
                i = min_price_idx + 1
            else:
                i += 1
        
        print(f"Total down-moves detected: {len(down_moves)}")
        return down_moves
    
    def analyze_recovery_patterns(self, down_moves: List[Dict], trades_df: pd.DataFrame, 
                                measurement_times: List[int] = [60, 90, 300]) -> List[Dict]:
        """
        Analyze price recovery after down-moves
        
        Args:
            down_moves: List of detected down-moves
            trades_df: DataFrame with trades data
            measurement_times: Time points (in seconds) to measure recovery
            
        Returns:
            Enhanced down-moves with recovery data
        """
        enhanced_moves = []
        
        for move in down_moves:
            enhanced_move = move.copy()
            
            # Find the price at each measurement time
            for measurement_time in measurement_times:
                target_time = move['end_time'] + timedelta(seconds=measurement_time)
                
                # Find the closest trade to the target time
                time_diffs = abs(trades_df['time'] - target_time)
                closest_idx = time_diffs.idxmin()
                
                if pd.notna(closest_idx):
                    closest_trade = trades_df.loc[closest_idx]
                    recovery_price = closest_trade['price']
                    
                    # Calculate recovery percentage from the minimum price
                    recovery_pct = ((recovery_price - move['min_price']) / move['min_price']) * 100
                    
                    # Calculate net return from start price
                    net_return_pct = ((recovery_price - move['start_price']) / move['start_price']) * 100
                    
                    enhanced_move[f'price_at_{measurement_time}s'] = recovery_price
                    enhanced_move[f'recovery_pct_{measurement_time}s'] = recovery_pct
                    enhanced_move[f'net_return_pct_{measurement_time}s'] = net_return_pct
                    enhanced_move[f'time_diff_{measurement_time}s'] = time_diffs.loc[closest_idx].total_seconds()
                else:
                    enhanced_move[f'price_at_{measurement_time}s'] = None
                    enhanced_move[f'recovery_pct_{measurement_time}s'] = None
                    enhanced_move[f'net_return_pct_{measurement_time}s'] = None
                    enhanced_move[f'time_diff_{measurement_time}s'] = None
            
            enhanced_moves.append(enhanced_move)
        
        return enhanced_moves

def test_movement_detector():
    """Test the movement detector with sample data"""
    from pathlib import Path
    
    # Load sample data
    processor = BinanceDataProcessor()
    
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    
    if not trades_file.exists() or not klines_file.exists():
        print("Sample data files not found. Please run the download script first.")
        return
    
    print("=== Testing Price Movement Detector ===")
    
    # Load data
    print("Loading data...")
    trades_df = processor.load_trades_from_zip(trades_file)
    klines_df = processor.load_klines_from_zip(klines_file)
    
    # Use a smaller subset for testing (first 30 minutes)
    start_time = trades_df['time'].min()
    end_time = start_time + timedelta(minutes=30)

    trades_subset = trades_df[(trades_df['time'] >= start_time) & (trades_df['time'] <= end_time)]
    klines_subset = klines_df[(klines_df['open_time'] >= start_time) & (klines_df['open_time'] <= end_time)]

    print(f"Using subset: {len(trades_subset)} trades, {len(klines_subset)} klines")
    
    # Initialize detector
    detector = PriceMovementDetector(down_move_threshold=0.05, max_duration_seconds=120)
    
    # Detect down-moves from trades
    print("\n--- Detecting from Trades ---")
    down_moves_trades = detector.detect_down_moves_from_trades(trades_subset)
    
    # Detect down-moves from klines
    print("\n--- Detecting from Klines ---")
    down_moves_klines = detector.detect_down_moves_from_klines(klines_subset)
    
    # Analyze recovery patterns for trades-based detection
    if down_moves_trades:
        print("\n--- Analyzing Recovery Patterns ---")
        enhanced_moves = detector.analyze_recovery_patterns(down_moves_trades, trades_df)
        
        print("\nSample enhanced down-move:")
        if enhanced_moves:
            move = enhanced_moves[0]
            print(f"Start: {move['start_time']}, Price: {move['start_price']:.2f}")
            print(f"Min: {move['end_time']}, Price: {move['min_price']:.2f}")
            print(f"Drop: {move['price_drop_pct']:.2f}% in {move['duration_seconds']:.1f}s")
            
            for time_point in [60, 90, 300]:
                if f'price_at_{time_point}s' in move and move[f'price_at_{time_point}s'] is not None:
                    print(f"At +{time_point}s: {move[f'price_at_{time_point}s']:.2f} "
                          f"(recovery: {move[f'recovery_pct_{time_point}s']:.2f}%, "
                          f"net: {move[f'net_return_pct_{time_point}s']:.2f}%)")

if __name__ == "__main__":
    test_movement_detector()
