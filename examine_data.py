#!/usr/bin/env python3
"""
Examine the structure of downloaded Binance data
"""

import pandas as pd
import zipfile
from pathlib import Path

def examine_klines_data():
    """Examine the 1s klines data structure"""
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    
    if not klines_file.exists():
        print("Klines file not found")
        return
    
    print("=== Examining Klines Data ===")
    
    with zipfile.ZipFile(klines_file, 'r') as zip_ref:
        # List files in zip
        file_list = zip_ref.namelist()
        print(f"Files in zip: {file_list}")
        
        # Read the CSV file
        csv_file = file_list[0]  # Should be the CSV file
        
        with zip_ref.open(csv_file) as f:
            # Read first few lines to understand structure
            df = pd.read_csv(f, nrows=10)
            print(f"\nKlines data shape: {df.shape}")
            print(f"Columns: {df.columns.tolist()}")
            print("\nFirst 5 rows:")
            print(df.head())
            
            # Check data types
            print(f"\nData types:")
            print(df.dtypes)

def examine_trades_data():
    """Examine the trades data structure"""
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    
    if not trades_file.exists():
        print("Trades file not found")
        return
    
    print("\n=== Examining Trades Data ===")
    
    with zipfile.ZipFile(trades_file, 'r') as zip_ref:
        # List files in zip
        file_list = zip_ref.namelist()
        print(f"Files in zip: {file_list}")
        
        # Read the CSV file
        csv_file = file_list[0]  # Should be the CSV file
        
        with zip_ref.open(csv_file) as f:
            # Read first few lines to understand structure
            df = pd.read_csv(f, nrows=10)
            print(f"\nTrades data shape: {df.shape}")
            print(f"Columns: {df.columns.tolist()}")
            print("\nFirst 5 rows:")
            print(df.head())
            
            # Check data types
            print(f"\nData types:")
            print(df.dtypes)

def analyze_data_frequency():
    """Analyze the frequency of data points"""
    print("\n=== Analyzing Data Frequency ===")
    
    # Check klines frequency
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    if klines_file.exists():
        with zipfile.ZipFile(klines_file, 'r') as zip_ref:
            csv_file = zip_ref.namelist()[0]
            with zip_ref.open(csv_file) as f:
                df = pd.read_csv(f, nrows=1000)
                
                # Assuming first column is timestamp
                if len(df.columns) > 0:
                    timestamps = df.iloc[:, 0]  # First column should be open time
                    print(f"Klines: First timestamp: {timestamps.iloc[0]}")
                    print(f"Klines: Last timestamp: {timestamps.iloc[-1]}")
                    
                    # Calculate time differences
                    if len(timestamps) > 1:
                        time_diff = timestamps.iloc[1] - timestamps.iloc[0]
                        print(f"Klines: Time difference between first two records: {time_diff} ms")
    
    # Check trades frequency
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    if trades_file.exists():
        with zipfile.ZipFile(trades_file, 'r') as zip_ref:
            csv_file = zip_ref.namelist()[0]
            with zip_ref.open(csv_file) as f:
                df = pd.read_csv(f, nrows=1000)
                
                # Find timestamp column (usually last or second to last)
                print(f"Trades: Sample of timestamp column:")
                if len(df.columns) >= 5:  # Trades should have at least 5 columns
                    timestamp_col = df.iloc[:, -2]  # Usually second to last column
                    print(f"First few timestamps: {timestamp_col.head()}")

if __name__ == "__main__":
    examine_klines_data()
    examine_trades_data()
    analyze_data_frequency()
