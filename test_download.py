#!/usr/bin/env python3
"""
Test script to download a small sample of Binance data
"""

import os
import sys
from datetime import datetime, timedelta

# Add the binance scripts to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'data/binance_scripts/python'))

from utility import download_file, get_path

def test_download():
    """Test downloading one day of data for BTCUSDT"""
    symbol = "BTCUSDT"
    trading_type = "spot"
    test_date = "2024-08-15"  # Recent date
    data_folder = "data/raw"
    
    print(f"Testing download for {symbol} on {test_date}")
    
    # Test downloading 1s klines
    try:
        interval = "1s"
        path = get_path(trading_type, "klines", "daily", symbol, interval)
        file_name = f"{symbol.upper()}-{interval}-{test_date}.zip"
        
        print(f"Downloading {file_name}")
        download_file(path, file_name, None, data_folder)
        print("✓ Klines download successful")
        
    except Exception as e:
        print(f"✗ Klines download failed: {e}")
    
    # Test downloading trades
    try:
        path = get_path(trading_type, "trades", "daily", symbol)
        file_name = f"{symbol.upper()}-trades-{test_date}.zip"
        
        print(f"Downloading {file_name}")
        download_file(path, file_name, None, data_folder)
        print("✓ Trades download successful")
        
    except Exception as e:
        print(f"✗ Trades download failed: {e}")

if __name__ == "__main__":
    test_download()
