#!/usr/bin/env python3
"""
Project structure setup for Binance TAQ Data Analysis
Creates necessary directories and initializes the project
"""

import os
from pathlib import Path

def create_project_structure():
    """Create the project directory structure"""
    
    # Define project directories
    directories = [
        'data',
        'data/raw',
        'data/processed',
        'data/binance_scripts',
        'src',
        'src/data_acquisition',
        'src/analysis',
        'src/utils',
        'results',
        'results/plots',
        'results/reports',
        'logs'
    ]
    
    # Create directories
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Create __init__.py files for Python packages
    init_files = [
        'src/__init__.py',
        'src/data_acquisition/__init__.py',
        'src/analysis/__init__.py',
        'src/utils/__init__.py'
    ]
    
    for init_file in init_files:
        Path(init_file).touch()
        print(f"Created file: {init_file}")
    
    print("\nProject structure created successfully!")
    print("\nDirectory structure:")
    print("├── data/")
    print("│   ├── raw/")
    print("│   ├── processed/")
    print("│   └── binance_scripts/")
    print("├── src/")
    print("│   ├── data_acquisition/")
    print("│   ├── analysis/")
    print("│   └── utils/")
    print("├── results/")
    print("│   ├── plots/")
    print("│   └── reports/")
    print("└── logs/")

if __name__ == "__main__":
    create_project_structure()
