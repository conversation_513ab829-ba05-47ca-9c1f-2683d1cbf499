#!/usr/bin/env python3
"""
Download full 3-month dataset for BTCUSDT and ETHUSDT
"""

import urllib.request
from pathlib import Path
from datetime import datetime, timedelta
import time

def download_full_dataset():
    """Download last 3 months of data for both symbols"""
    
    # Create data directory
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    symbols = ["BTCUSDT", "ETHUSDT"]
    base_url = "https://data.binance.vision/"
    
    # Calculate date range (last 3 months)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    
    print(f"Downloading data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Generate list of dates
    dates = []
    current = start_date
    while current <= end_date:
        dates.append(current.strftime("%Y-%m-%d"))
        current += timedelta(days=1)
    
    print(f"Total dates to download: {len(dates)}")
    
    total_files = len(symbols) * len(dates) * 2  # 2 file types per symbol per date
    downloaded = 0
    failed = 0
    
    for symbol in symbols:
        print(f"\n=== Downloading {symbol} data ===")
        
        for date_str in dates:
            print(f"Downloading {symbol} for {date_str}")
            
            # Download trades data
            trades_url = f"{base_url}data/spot/daily/trades/{symbol}/{symbol}-trades-{date_str}.zip"
            trades_file = data_dir / f"{symbol}-trades-{date_str}.zip"
            
            if not trades_file.exists():
                try:
                    print(f"  Downloading trades...")
                    urllib.request.urlretrieve(trades_url, trades_file)
                    print(f"  ✓ Trades downloaded ({trades_file.stat().st_size} bytes)")
                    downloaded += 1
                    time.sleep(0.1)  # Small delay to be respectful
                except Exception as e:
                    print(f"  ✗ Trades download failed: {e}")
                    failed += 1
            else:
                print(f"  ✓ Trades already exists")
                downloaded += 1
            
            # Download 1s klines data
            klines_url = f"{base_url}data/spot/daily/klines/{symbol}/1s/{symbol}-1s-{date_str}.zip"
            klines_file = data_dir / f"{symbol}-1s-{date_str}.zip"
            
            if not klines_file.exists():
                try:
                    print(f"  Downloading 1s klines...")
                    urllib.request.urlretrieve(klines_url, klines_file)
                    print(f"  ✓ Klines downloaded ({klines_file.stat().st_size} bytes)")
                    downloaded += 1
                    time.sleep(0.1)  # Small delay to be respectful
                except Exception as e:
                    print(f"  ✗ Klines download failed: {e}")
                    failed += 1
            else:
                print(f"  ✓ Klines already exists")
                downloaded += 1
    
    print(f"\n=== Download Summary ===")
    print(f"Total files: {total_files}")
    print(f"Downloaded/Existing: {downloaded}")
    print(f"Failed: {failed}")
    print(f"Success rate: {(downloaded/total_files)*100:.1f}%")
    
    # Calculate total data size
    total_size = sum(f.stat().st_size for f in data_dir.glob("*.zip"))
    print(f"Total data size: {total_size / (1024**3):.2f} GB")

if __name__ == "__main__":
    print("Starting full dataset download...")
    print("This will download approximately 3 months of high-frequency data")
    print("Expected size: Several GB")
    
    response = input("Continue? (y/n): ")
    if response.lower() == 'y':
        download_full_dataset()
    else:
        print("Download cancelled")
