# Binance TAQ Analysis - Usage Guide

## Quick Start

### 1. Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Create project structure
python project_structure.py
```

### 2. Download Sample Data (for testing)
```bash
# Download one day of sample data
python test_download_simple.py
```

### 3. Run Sample Analysis
```bash
# Analyze the sample data
python src/analysis/comprehensive_analysis.py
```

### 4. Download Full Dataset (for complete analysis)
```bash
# Download 3 months of data (several GB)
python download_full_dataset.py
```

## Detailed Usage

### Data Download Options

#### Option 1: Sample Data (Quick Test)
```bash
python test_download_simple.py
```
- Downloads: 1 day of BTCUSDT data
- Size: ~30 MB
- Purpose: Testing and development

#### Option 2: Full Dataset
```bash
python download_full_dataset.py
```
- Downloads: 3 months of BTCUSDT + ETHUSDT data
- Size: Several GB
- Purpose: Complete analysis

#### Option 3: Custom Download
```python
from src.data_acquisition.binance_taq_downloader import BinanceTAQDownloader

downloader = BinanceTAQDownloader()
downloader.download_all_data()
```

### Analysis Workflows

#### Workflow 1: Complete Automated Analysis
```bash
python src/analysis/comprehensive_analysis.py
```

**What it does:**
- Finds all available data files
- Analyzes each day separately
- Combines results across all days
- Generates summary reports
- Saves detailed JSON results

**Output files:**
- `results/BTCUSDT_analysis_results.json`
- `results/BTCUSDT_summary_report.txt`
- `results/ETHUSDT_analysis_results.json`
- `results/ETHUSDT_summary_report.txt`

#### Workflow 2: Single Day Analysis
```python
from src.analysis.comprehensive_analysis import ComprehensiveAnalyzer

analyzer = ComprehensiveAnalyzer()
result = analyzer.analyze_single_day("BTCUSDT", "2024-08-15")
```

#### Workflow 3: Custom Analysis
```python
from src.utils.data_processor import BinanceDataProcessor
from src.analysis.price_movement_detector import PriceMovementDetector
from src.analysis.performance_calculator import PerformanceCalculator

# Load data
processor = BinanceDataProcessor()
trades_df = processor.load_trades_from_zip("data/raw/BTCUSDT-trades-2024-08-15.zip")
klines_df = processor.load_klines_from_zip("data/raw/BTCUSDT-1s-2024-08-15.zip")

# Detect down-moves
detector = PriceMovementDetector(down_move_threshold=0.05, max_duration_seconds=120)
down_moves = detector.detect_down_moves_from_klines(klines_df)

# Calculate performance
calculator = PerformanceCalculator(taker_fee_rate=0.001)
enhanced_moves = calculator.calculate_net_returns(down_moves, trades_df)
```

### Configuration Options

#### Price Movement Detection
```python
detector = PriceMovementDetector(
    down_move_threshold=0.05,    # 5% minimum drop
    max_duration_seconds=120     # Within 120 seconds
)
```

**Parameters:**
- `down_move_threshold`: Minimum percentage drop (0.05 = 5%)
- `max_duration_seconds`: Maximum time window for the move

#### Performance Calculation
```python
calculator = PerformanceCalculator(
    taker_fee_rate=0.001,        # 0.1% taker fee
    maker_fee_rate=0.001         # 0.1% maker fee
)
```

**Parameters:**
- `taker_fee_rate`: Fee rate for market orders
- `maker_fee_rate`: Fee rate for limit orders

#### Measurement Timeframes
```python
measurement_times = [60, 90, 300]  # Seconds after down-move
```

### Understanding the Results

#### JSON Output Structure
```json
{
  "symbol": "BTCUSDT",
  "analysis_period": "30 days",
  "total_down_moves": 5,
  "overall_summary": {
    "60s": {
      "count": 5,
      "gross_return_mean": 2.1,
      "net_return_mean": 1.9,
      "avg_transaction_cost": 0.2,
      "win_rate": 80.0
    }
  },
  "all_down_moves": [...]
}
```

#### Key Metrics Explained

**Gross Return**: Price change without transaction costs
**Net Return**: Price change after subtracting fees and spreads
**Transaction Cost**: Total cost including fees and spread
**Win Rate**: Percentage of trades with positive net returns

#### Individual Down-Move Data
```json
{
  "start_time": "2024-08-15T10:00:00",
  "end_time": "2024-08-15T10:01:30",
  "start_price": 58000.0,
  "min_price": 57100.0,
  "price_drop_pct": 1.55,
  "duration_seconds": 90,
  "net_return_pct_60s": 0.85,
  "net_return_pct_90s": 1.20,
  "net_return_pct_300s": 0.95
}
```

### Troubleshooting

#### Common Issues

**1. No data files found**
```
Error: No data files found. Please run the data download script first.
```
**Solution**: Run `python test_download_simple.py` or `python download_full_dataset.py`

**2. Memory issues with large datasets**
```
MemoryError: Unable to allocate array
```
**Solution**: Process data in smaller chunks or use a machine with more RAM

**3. No down-moves detected**
```
No 5% down-moves detected in the analyzed period.
```
**Explanation**: This is normal for stable market periods. 5% moves in 120 seconds are rare events.

#### Performance Tips

**1. Use SSD storage** for faster data loading
**2. Increase RAM** for processing large datasets
**3. Process data in chunks** for very large analyses
**4. Use the klines-based detection** for better performance

### Advanced Usage

#### Custom Thresholds
```python
# Detect smaller moves
detector = PriceMovementDetector(down_move_threshold=0.01, max_duration_seconds=60)

# Detect larger moves with longer timeframe
detector = PriceMovementDetector(down_move_threshold=0.10, max_duration_seconds=300)
```

#### Different Fee Structures
```python
# High-frequency trader rates
calculator = PerformanceCalculator(taker_fee_rate=0.0004, maker_fee_rate=0.0002)

# Retail trader rates
calculator = PerformanceCalculator(taker_fee_rate=0.001, maker_fee_rate=0.001)
```

#### Custom Analysis Periods
```python
# Analyze specific date range
analyzer = ComprehensiveAnalyzer()
results = analyzer.analyze_multiple_days("BTCUSDT", max_days=7)  # Last 7 days
```

### Data Quality Checks

#### Verify Download Integrity
```python
from pathlib import Path

data_dir = Path("data/raw")
for file in data_dir.glob("*.zip"):
    if file.stat().st_size == 0:
        print(f"Empty file: {file}")
```

#### Check Data Coverage
```python
from src.analysis.comprehensive_analysis import ComprehensiveAnalyzer

analyzer = ComprehensiveAnalyzer()
available_files = analyzer.find_available_data_files()
print(available_files)
```

### Extending the Analysis

#### Add New Symbols
```python
# Modify the symbols list in comprehensive_analysis.py
self.symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT"]
```

#### Add New Metrics
```python
# Extend the performance calculator
def calculate_sharpe_ratio(self, returns):
    return np.mean(returns) / np.std(returns)
```

#### Custom Reporting
```python
# Create custom report formats
def generate_csv_report(self, results):
    df = pd.DataFrame(results['all_down_moves'])
    df.to_csv('custom_report.csv', index=False)
```

## Support

For questions or issues:
1. Check the troubleshooting section above
2. Review the code documentation
3. Examine the sample outputs in the `results/` directory
