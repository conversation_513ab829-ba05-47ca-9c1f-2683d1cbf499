#!/usr/bin/env python3
"""
Data processor for Binance TAQ data
Handles loading and processing of klines and trades data
"""

import pandas as pd
import zipfile
import numpy as np
from pathlib import Path
from typing import List, Tuple, Optional

class BinanceDataProcessor:
    """Process Binance klines and trades data"""
    
    # Column definitions based on Binance API documentation
    KLINES_COLUMNS = [
        'open_time',      # Open time (timestamp in ms)
        'open',           # Open price
        'high',           # High price
        'low',            # Low price
        'close',          # Close price
        'volume',         # Volume
        'close_time',     # Close time (timestamp in ms)
        'quote_volume',   # Quote asset volume
        'count',          # Number of trades
        'taker_buy_volume',      # Taker buy base asset volume
        'taker_buy_quote_volume', # Taker buy quote asset volume
        'ignore'          # Ignore field
    ]
    
    TRADES_COLUMNS = [
        'trade_id',       # Trade ID
        'price',          # Price
        'quantity',       # Quantity
        'quote_quantity', # Quote quantity
        'time',           # Time (timestamp in ms)
        'is_buyer_maker', # Was the buyer the maker?
        'is_best_match'   # Was the trade the best price match?
    ]
    
    def __init__(self):
        pass
    
    def load_klines_from_zip(self, zip_path: Path) -> pd.DataFrame:
        """Load klines data from a zip file"""
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            csv_file = zip_ref.namelist()[0]
            
            with zip_ref.open(csv_file) as f:
                df = pd.read_csv(f, header=None, names=self.KLINES_COLUMNS)
                
                # Convert timestamps to datetime
                df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
                
                # Convert price columns to float
                price_cols = ['open', 'high', 'low', 'close']
                for col in price_cols:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Convert volume columns to float
                volume_cols = ['volume', 'quote_volume', 'taker_buy_volume', 'taker_buy_quote_volume']
                for col in volume_cols:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Sort by time
                df = df.sort_values('open_time').reset_index(drop=True)
                
                return df
    
    def load_trades_from_zip(self, zip_path: Path) -> pd.DataFrame:
        """Load trades data from a zip file"""
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            csv_file = zip_ref.namelist()[0]
            
            with zip_ref.open(csv_file) as f:
                df = pd.read_csv(f, header=None, names=self.TRADES_COLUMNS)
                
                # Convert timestamp to datetime
                df['time'] = pd.to_datetime(df['time'], unit='ms')
                
                # Convert price and quantity columns to float
                numeric_cols = ['price', 'quantity', 'quote_quantity']
                for col in numeric_cols:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Convert boolean columns
                df['is_buyer_maker'] = df['is_buyer_maker'].astype(bool)
                df['is_best_match'] = df['is_best_match'].astype(bool)
                
                # Sort by time
                df = df.sort_values('time').reset_index(drop=True)
                
                return df
    
    def calculate_mid_price(self, df: pd.DataFrame, price_col: str = 'close') -> pd.Series:
        """Calculate mid price from klines data (using close price as proxy)"""
        return df[price_col]
    
    def calculate_spread_from_trades(self, trades_df: pd.DataFrame, window_ms: int = 1000) -> pd.DataFrame:
        """
        Calculate bid-ask spread from trades data
        This is an approximation since we don't have order book data
        """
        # Group trades by time windows
        trades_df['time_window'] = trades_df['time'].dt.floor(f'{window_ms}ms')
        
        # Calculate approximate bid/ask from buyer/seller trades
        spread_data = []
        
        for time_window, group in trades_df.groupby('time_window'):
            if len(group) == 0:
                continue
                
            # Approximate bid as average price of seller-initiated trades
            # Approximate ask as average price of buyer-initiated trades
            seller_trades = group[group['is_buyer_maker'] == True]  # Seller initiated
            buyer_trades = group[group['is_buyer_maker'] == False]  # Buyer initiated
            
            if len(seller_trades) > 0 and len(buyer_trades) > 0:
                bid = seller_trades['price'].mean()
                ask = buyer_trades['price'].mean()
                spread = ask - bid
                mid_price = (bid + ask) / 2
                
                spread_data.append({
                    'time': time_window,
                    'bid': bid,
                    'ask': ask,
                    'spread': spread,
                    'mid_price': mid_price,
                    'spread_bps': (spread / mid_price) * 10000  # Spread in basis points
                })
        
        return pd.DataFrame(spread_data)
    
    def resample_to_frequency(self, df: pd.DataFrame, freq: str, time_col: str = 'time') -> pd.DataFrame:
        """Resample data to specified frequency"""
        df_copy = df.copy()
        df_copy = df_copy.set_index(time_col)
        
        # Define aggregation rules
        agg_rules = {}
        
        if 'price' in df_copy.columns:
            agg_rules['price'] = 'last'
        if 'open' in df_copy.columns:
            agg_rules.update({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            })
        
        # Add other numeric columns
        for col in df_copy.select_dtypes(include=[np.number]).columns:
            if col not in agg_rules:
                agg_rules[col] = 'last'
        
        resampled = df_copy.resample(freq).agg(agg_rules)
        return resampled.reset_index()

def test_data_processor():
    """Test the data processor with our sample data"""
    processor = BinanceDataProcessor()
    
    # Test klines processing
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    if klines_file.exists():
        print("=== Testing Klines Processing ===")
        klines_df = processor.load_klines_from_zip(klines_file)
        print(f"Loaded {len(klines_df)} klines records")
        print(f"Time range: {klines_df['open_time'].min()} to {klines_df['open_time'].max()}")
        print(f"Price range: {klines_df['close'].min():.2f} to {klines_df['close'].max():.2f}")
        print("\nFirst 3 records:")
        print(klines_df[['open_time', 'open', 'high', 'low', 'close', 'volume']].head(3))
    
    # Test trades processing
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    if trades_file.exists():
        print("\n=== Testing Trades Processing ===")
        trades_df = processor.load_trades_from_zip(trades_file)
        print(f"Loaded {len(trades_df)} trade records")
        print(f"Time range: {trades_df['time'].min()} to {trades_df['time'].max()}")
        print(f"Price range: {trades_df['price'].min():.2f} to {trades_df['price'].max():.2f}")
        print("\nFirst 3 records:")
        print(trades_df[['time', 'price', 'quantity', 'is_buyer_maker']].head(3))
        
        # Test spread calculation
        print("\n=== Testing Spread Calculation ===")
        spread_df = processor.calculate_spread_from_trades(trades_df.head(1000))  # Use first 1000 trades
        if len(spread_df) > 0:
            print(f"Calculated spread for {len(spread_df)} time windows")
            print("Sample spread data:")
            print(spread_df[['time', 'bid', 'ask', 'spread', 'spread_bps']].head(3))

if __name__ == "__main__":
    test_data_processor()
