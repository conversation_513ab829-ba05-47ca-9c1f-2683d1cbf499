#!/usr/bin/env python3
"""
Analyze price movements to understand typical volatility
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import os

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from utils.data_processor import BinanceDataProcessor
from analysis.price_movement_detector import PriceMovementDetector

def analyze_price_volatility():
    """Analyze typical price movements in the data"""
    
    processor = BinanceDataProcessor()
    
    # Load sample data
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    
    if not trades_file.exists() or not klines_file.exists():
        print("Sample data files not found.")
        return
    
    print("=== Analyzing Price Volatility ===")
    
    # Load klines data (more efficient for analysis)
    klines_df = processor.load_klines_from_zip(klines_file)
    
    # Calculate rolling statistics
    klines_df['price_change'] = klines_df['close'].pct_change()
    klines_df['price_change_abs'] = klines_df['price_change'].abs()
    
    # Calculate rolling max/min over different windows
    for window in [60, 120, 300]:  # 1 min, 2 min, 5 min
        klines_df[f'rolling_max_{window}'] = klines_df['close'].rolling(window).max()
        klines_df[f'rolling_min_{window}'] = klines_df['close'].rolling(window).min()
        klines_df[f'max_drop_{window}'] = (klines_df['close'] - klines_df[f'rolling_min_{window}']) / klines_df['close']
        klines_df[f'max_gain_{window}'] = (klines_df[f'rolling_max_{window}'] - klines_df['close']) / klines_df['close']
    
    print(f"Loaded {len(klines_df)} 1-second klines")
    print(f"Price range: {klines_df['close'].min():.2f} to {klines_df['close'].max():.2f}")
    
    # Analyze price change statistics
    print("\n=== Price Change Statistics ===")
    print(f"Mean absolute price change per second: {klines_df['price_change_abs'].mean()*100:.4f}%")
    print(f"Max price change per second: {klines_df['price_change_abs'].max()*100:.4f}%")
    print(f"95th percentile price change: {klines_df['price_change_abs'].quantile(0.95)*100:.4f}%")
    print(f"99th percentile price change: {klines_df['price_change_abs'].quantile(0.99)*100:.4f}%")
    
    # Analyze maximum drops over different windows
    print("\n=== Maximum Drops Analysis ===")
    for window in [60, 120, 300]:
        max_drops = klines_df[f'max_drop_{window}'].dropna()
        print(f"\n{window}-second window:")
        print(f"  Max drop observed: {max_drops.max()*100:.4f}%")
        print(f"  95th percentile drop: {max_drops.quantile(0.95)*100:.4f}%")
        print(f"  99th percentile drop: {max_drops.quantile(0.99)*100:.4f}%")
        print(f"  Drops > 1%: {(max_drops > 0.01).sum()}")
        print(f"  Drops > 2%: {(max_drops > 0.02).sum()}")
        print(f"  Drops > 5%: {(max_drops > 0.05).sum()}")

def test_with_smaller_thresholds():
    """Test detection with smaller movement thresholds"""
    
    processor = BinanceDataProcessor()
    
    # Load sample data
    klines_file = Path("data/raw/BTCUSDT-1s-2024-08-15.zip")
    
    if not klines_file.exists():
        print("Sample data files not found.")
        return
    
    print("\n=== Testing with Smaller Thresholds ===")
    
    klines_df = processor.load_klines_from_zip(klines_file)
    
    # Use first hour for testing
    start_time = klines_df['open_time'].min()
    end_time = start_time + pd.Timedelta(hours=1)
    klines_subset = klines_df[(klines_df['open_time'] >= start_time) & (klines_df['open_time'] <= end_time)]
    
    print(f"Testing with {len(klines_subset)} klines from first hour")
    
    # Test with different thresholds
    thresholds = [0.005, 0.01, 0.02]  # 0.5%, 1%, 2%
    
    for threshold in thresholds:
        print(f"\n--- Testing {threshold*100}% threshold ---")
        detector = PriceMovementDetector(down_move_threshold=threshold, max_duration_seconds=120)
        down_moves = detector.detect_down_moves_from_klines(klines_subset)
        
        if down_moves:
            print(f"Found {len(down_moves)} down-moves")
            for i, move in enumerate(down_moves[:3]):  # Show first 3
                print(f"  Move {i+1}: {move['price_drop_pct']:.3f}% drop in {move['duration_seconds']:.1f}s")
        else:
            print("No down-moves found")

if __name__ == "__main__":
    analyze_price_volatility()
    test_with_smaller_thresholds()
