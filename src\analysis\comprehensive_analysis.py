#!/usr/bin/env python3
"""
Comprehensive Binance TAQ Analysis
Main script to run the complete analysis pipeline
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime, timedelta
from typing import Dict, List
import sys
import os

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.data_processor import BinanceDataProcessor
from analysis.price_movement_detector import PriceMovementDetector
from analysis.performance_calculator import PerformanceCalculator

class ComprehensiveAnalyzer:
    """Main analyzer class that orchestrates the complete analysis"""
    
    def __init__(self, data_folder: str = "data/raw", results_folder: str = "results"):
        self.data_folder = Path(data_folder)
        self.results_folder = Path(results_folder)
        self.results_folder.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.processor = BinanceDataProcessor()
        self.detector = PriceMovementDetector(down_move_threshold=0.05, max_duration_seconds=120)
        self.calculator = PerformanceCalculator(taker_fee_rate=0.001)  # 0.1% taker fee
        
        self.symbols = ["BTCUSDT", "ETHUSDT"]
        
    def find_available_data_files(self) -> Dict[str, List[Path]]:
        """Find all available data files for analysis"""
        available_files = {}
        
        for symbol in self.symbols:
            trades_files = list(self.data_folder.glob(f"{symbol}-trades-*.zip"))
            klines_files = list(self.data_folder.glob(f"{symbol}-1s-*.zip"))
            
            available_files[symbol] = {
                'trades': sorted(trades_files),
                'klines': sorted(klines_files)
            }
            
            print(f"{symbol}: Found {len(trades_files)} trade files, {len(klines_files)} kline files")
        
        return available_files
    
    def analyze_single_day(self, symbol: str, date_str: str) -> Dict:
        """Analyze a single day of data for one symbol"""
        
        trades_file = self.data_folder / f"{symbol}-trades-{date_str}.zip"
        klines_file = self.data_folder / f"{symbol}-1s-{date_str}.zip"
        
        if not trades_file.exists() or not klines_file.exists():
            print(f"Data files not found for {symbol} on {date_str}")
            return None
        
        print(f"\nAnalyzing {symbol} for {date_str}")
        
        try:
            # Load data
            trades_df = self.processor.load_trades_from_zip(trades_file)
            klines_df = self.processor.load_klines_from_zip(klines_file)
            
            print(f"Loaded {len(trades_df)} trades, {len(klines_df)} klines")
            
            # Detect down-moves using klines (more efficient)
            down_moves = self.detector.detect_down_moves_from_klines(klines_df)
            
            if not down_moves:
                print("No 5% down-moves detected")
                return {
                    'symbol': symbol,
                    'date': date_str,
                    'trades_count': len(trades_df),
                    'klines_count': len(klines_df),
                    'down_moves_count': 0,
                    'down_moves': [],
                    'performance_summary': None
                }
            
            # Calculate performance with transaction costs
            enhanced_moves = self.calculator.calculate_net_returns(down_moves, trades_df)
            
            # Generate summary statistics
            performance_summary = self.calculator.generate_performance_summary(enhanced_moves)
            
            print(f"Analysis complete: {len(down_moves)} down-moves detected")
            
            return {
                'symbol': symbol,
                'date': date_str,
                'trades_count': len(trades_df),
                'klines_count': len(klines_df),
                'down_moves_count': len(down_moves),
                'down_moves': enhanced_moves,
                'performance_summary': performance_summary
            }
            
        except Exception as e:
            print(f"Error analyzing {symbol} on {date_str}: {e}")
            return None
    
    def analyze_multiple_days(self, symbol: str, max_days: int = 30) -> Dict:
        """Analyze multiple days of data for one symbol"""
        
        # Find available files
        available_files = self.find_available_data_files()
        
        if symbol not in available_files:
            print(f"No data found for {symbol}")
            return None
        
        trades_files = available_files[symbol]['trades'][:max_days]
        
        all_results = []
        all_down_moves = []
        
        print(f"\nAnalyzing {len(trades_files)} days of {symbol} data")
        
        for trades_file in trades_files:
            # Extract date from filename
            date_str = trades_file.stem.split('-trades-')[1]
            
            result = self.analyze_single_day(symbol, date_str)
            
            if result:
                all_results.append(result)
                all_down_moves.extend(result['down_moves'])
        
        # Generate overall summary
        if all_down_moves:
            overall_summary = self.calculator.generate_performance_summary(all_down_moves)
        else:
            overall_summary = {'total_down_moves': 0, 'message': 'No down-moves found'}
        
        return {
            'symbol': symbol,
            'analysis_period': f"{len(all_results)} days",
            'total_down_moves': len(all_down_moves),
            'daily_results': all_results,
            'overall_summary': overall_summary,
            'all_down_moves': all_down_moves
        }
    
    def save_results(self, results: Dict, filename: str):
        """Save analysis results to JSON file"""
        
        # Convert timestamps to strings for JSON serialization
        def convert_timestamps(obj):
            if isinstance(obj, pd.Timestamp):
                return obj.isoformat()
            elif isinstance(obj, dict):
                return {k: convert_timestamps(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_timestamps(item) for item in obj]
            else:
                return obj
        
        results_serializable = convert_timestamps(results)
        
        output_file = self.results_folder / filename
        with open(output_file, 'w') as f:
            json.dump(results_serializable, f, indent=2, default=str)
        
        print(f"Results saved to {output_file}")
    
    def generate_summary_report(self, results: Dict) -> str:
        """Generate a human-readable summary report"""
        
        report = []
        report.append("=" * 60)
        report.append("BINANCE TAQ ANALYSIS SUMMARY REPORT")
        report.append("=" * 60)
        report.append(f"Symbol: {results['symbol']}")
        report.append(f"Analysis Period: {results['analysis_period']}")
        report.append(f"Total Down-moves Detected: {results['total_down_moves']}")
        report.append("")
        
        if results['total_down_moves'] > 0:
            summary = results['overall_summary']
            
            report.append("PERFORMANCE SUMMARY:")
            report.append("-" * 30)
            
            for time_point in [60, 90, 300]:
                if f'{time_point}s' in summary and 'count' in summary[f'{time_point}s']:
                    stats = summary[f'{time_point}s']
                    if stats['count'] > 0:
                        report.append(f"\nAt +{time_point} seconds:")
                        report.append(f"  Valid measurements: {stats['count']}")
                        report.append(f"  Average gross return: {stats['gross_return_mean']:.3f}%")
                        report.append(f"  Average net return: {stats['net_return_mean']:.3f}%")
                        report.append(f"  Average transaction cost: {stats['avg_transaction_cost']:.3f}%")
                        report.append(f"  Win rate: {stats['win_rate']:.1f}%")
                        report.append(f"  Positive returns: {stats['positive_returns']}")
                        report.append(f"  Negative returns: {stats['negative_returns']}")
            
            report.append("\nINDIVIDUAL DOWN-MOVES:")
            report.append("-" * 30)
            
            for i, move in enumerate(results['all_down_moves'][:10]):  # Show first 10
                report.append(f"\nMove {i+1}:")
                report.append(f"  Time: {move['start_time']}")
                report.append(f"  Drop: {move['price_drop_pct']:.2f}% in {move['duration_seconds']:.1f}s")
                report.append(f"  Price: {move['start_price']:.2f} → {move['min_price']:.2f}")
                
                for time_point in [60, 90, 300]:
                    net_key = f'net_return_pct_{time_point}s'
                    if net_key in move and move[net_key] is not None:
                        report.append(f"  Net return at +{time_point}s: {move[net_key]:.3f}%")
        else:
            report.append("No 5% down-moves detected in the analyzed period.")
            report.append("This suggests the market was relatively stable during this time.")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)

def main():
    """Main function to run the comprehensive analysis"""
    
    print("Starting Comprehensive Binance TAQ Analysis")
    print("=" * 50)
    
    analyzer = ComprehensiveAnalyzer()
    
    # Check available data
    available_files = analyzer.find_available_data_files()
    
    if not any(available_files.values()):
        print("No data files found. Please run the data download script first.")
        return
    
    # Analyze each symbol
    for symbol in analyzer.symbols:
        if symbol in available_files and available_files[symbol]['trades']:
            print(f"\nStarting analysis for {symbol}")
            
            # Analyze up to 30 days of data
            results = analyzer.analyze_multiple_days(symbol, max_days=30)
            
            if results:
                # Save detailed results
                analyzer.save_results(results, f"{symbol}_analysis_results.json")
                
                # Generate and save summary report
                report = analyzer.generate_summary_report(results)
                
                report_file = analyzer.results_folder / f"{symbol}_summary_report.txt"
                with open(report_file, 'w') as f:
                    f.write(report)
                
                print(f"Summary report saved to {report_file}")
                
                # Print summary to console
                print("\n" + report)
        else:
            print(f"No data available for {symbol}")
    
    print("\nAnalysis complete!")

if __name__ == "__main__":
    main()
