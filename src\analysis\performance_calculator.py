#!/usr/bin/env python3
"""
Performance Calculator for Binance TAQ Analysis
Calculates returns after transaction costs and spread
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from datetime import timedelta
import sys
import os

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.data_processor import BinanceDataProcessor

class PerformanceCalculator:
    """Calculate trading performance with transaction costs"""
    
    def __init__(self, taker_fee_rate: float = 0.001, maker_fee_rate: float = 0.001):
        """
        Initialize performance calculator
        
        Args:
            taker_fee_rate: Taker fee rate (0.001 = 0.1%)
            maker_fee_rate: Maker fee rate (0.001 = 0.1%)
        """
        self.taker_fee_rate = taker_fee_rate
        self.maker_fee_rate = maker_fee_rate
        self.processor = BinanceDataProcessor()
    
    def calculate_spread_cost(self, trades_df: pd.DataFrame, timestamp: pd.Timestamp, 
                            window_ms: int = 1000) -> float:
        """
        Calculate half-spread cost at a given timestamp
        
        Args:
            trades_df: DataFrame with trades data
            timestamp: Target timestamp
            window_ms: Window around timestamp to calculate spread
            
        Returns:
            Half-spread in basis points
        """
        # Find trades within the window around the timestamp
        start_time = timestamp - timedelta(milliseconds=window_ms//2)
        end_time = timestamp + timedelta(milliseconds=window_ms//2)
        
        window_trades = trades_df[
            (trades_df['time'] >= start_time) & 
            (trades_df['time'] <= end_time)
        ]
        
        if len(window_trades) == 0:
            return 0.0
        
        # Approximate bid/ask from buyer/seller initiated trades
        seller_trades = window_trades[window_trades['is_buyer_maker'] == True]
        buyer_trades = window_trades[window_trades['is_buyer_maker'] == False]
        
        if len(seller_trades) > 0 and len(buyer_trades) > 0:
            bid = seller_trades['price'].mean()
            ask = buyer_trades['price'].mean()
            mid_price = (bid + ask) / 2
            spread = ask - bid
            half_spread_bps = (spread / 2 / mid_price) * 10000
            return half_spread_bps
        else:
            # If we can't calculate spread, use a default estimate
            return 2.0  # 2 basis points as default half-spread
    
    def calculate_transaction_costs(self, entry_price: float, exit_price: float,
                                  entry_spread_bps: float, exit_spread_bps: float,
                                  position_size: float = 1.0) -> Dict[str, float]:
        """
        Calculate total transaction costs for a round-trip trade
        
        Args:
            entry_price: Entry price
            exit_price: Exit price
            entry_spread_bps: Half-spread at entry in basis points
            exit_spread_bps: Half-spread at exit in basis points
            position_size: Position size (default 1.0)
            
        Returns:
            Dictionary with cost breakdown
        """
        # Calculate fees
        entry_fee = entry_price * position_size * self.taker_fee_rate
        exit_fee = exit_price * position_size * self.taker_fee_rate
        total_fees = entry_fee + exit_fee
        
        # Calculate spread costs
        entry_spread_cost = entry_price * position_size * (entry_spread_bps / 10000)
        exit_spread_cost = exit_price * position_size * (exit_spread_bps / 10000)
        total_spread_cost = entry_spread_cost + exit_spread_cost
        
        # Total transaction costs
        total_costs = total_fees + total_spread_cost
        
        # Calculate as percentage of notional
        notional = entry_price * position_size
        total_cost_pct = (total_costs / notional) * 100
        
        return {
            'entry_fee': entry_fee,
            'exit_fee': exit_fee,
            'total_fees': total_fees,
            'entry_spread_cost': entry_spread_cost,
            'exit_spread_cost': exit_spread_cost,
            'total_spread_cost': total_spread_cost,
            'total_costs': total_costs,
            'total_cost_pct': total_cost_pct,
            'notional': notional
        }
    
    def calculate_net_returns(self, down_moves: List[Dict], trades_df: pd.DataFrame,
                            measurement_times: List[int] = [60, 90, 300]) -> List[Dict]:
        """
        Calculate net returns after transaction costs for each down-move
        
        Args:
            down_moves: List of detected down-moves
            trades_df: DataFrame with trades data
            measurement_times: Time points to measure returns
            
        Returns:
            Enhanced down-moves with net return calculations
        """
        enhanced_moves = []
        
        print(f"Calculating net returns for {len(down_moves)} down-moves...")
        
        for i, move in enumerate(down_moves):
            enhanced_move = move.copy()
            
            # Calculate entry spread cost (at the start of the down-move)
            entry_spread_bps = self.calculate_spread_cost(trades_df, move['start_time'])
            
            # For each measurement time
            for measurement_time in measurement_times:
                target_time = move['end_time'] + timedelta(seconds=measurement_time)
                
                # Find the closest trade to the target time
                time_diffs = abs(trades_df['time'] - target_time)
                closest_idx = time_diffs.idxmin()
                
                if pd.notna(closest_idx):
                    closest_trade = trades_df.loc[closest_idx]
                    exit_price = closest_trade['price']
                    exit_time = closest_trade['time']
                    
                    # Calculate exit spread cost
                    exit_spread_bps = self.calculate_spread_cost(trades_df, exit_time)
                    
                    # Calculate gross return
                    gross_return_pct = ((exit_price - move['start_price']) / move['start_price']) * 100
                    
                    # Calculate transaction costs
                    costs = self.calculate_transaction_costs(
                        move['start_price'], exit_price, 
                        entry_spread_bps, exit_spread_bps
                    )
                    
                    # Calculate net return
                    net_return_pct = gross_return_pct - costs['total_cost_pct']
                    
                    # Store results
                    enhanced_move[f'exit_price_{measurement_time}s'] = exit_price
                    enhanced_move[f'exit_time_{measurement_time}s'] = exit_time
                    enhanced_move[f'gross_return_pct_{measurement_time}s'] = gross_return_pct
                    enhanced_move[f'net_return_pct_{measurement_time}s'] = net_return_pct
                    enhanced_move[f'transaction_cost_pct_{measurement_time}s'] = costs['total_cost_pct']
                    enhanced_move[f'entry_spread_bps_{measurement_time}s'] = entry_spread_bps
                    enhanced_move[f'exit_spread_bps_{measurement_time}s'] = exit_spread_bps
                    enhanced_move[f'time_diff_{measurement_time}s'] = time_diffs.loc[closest_idx].total_seconds()
                else:
                    # No data available
                    for key in [f'exit_price_{measurement_time}s', f'exit_time_{measurement_time}s',
                               f'gross_return_pct_{measurement_time}s', f'net_return_pct_{measurement_time}s',
                               f'transaction_cost_pct_{measurement_time}s', f'entry_spread_bps_{measurement_time}s',
                               f'exit_spread_bps_{measurement_time}s', f'time_diff_{measurement_time}s']:
                        enhanced_move[key] = None
            
            enhanced_moves.append(enhanced_move)
            
            if (i + 1) % 10 == 0:
                print(f"Processed {i + 1}/{len(down_moves)} down-moves")
        
        return enhanced_moves
    
    def generate_performance_summary(self, enhanced_moves: List[Dict], 
                                   measurement_times: List[int] = [60, 90, 300]) -> Dict:
        """
        Generate summary statistics for the performance analysis
        
        Args:
            enhanced_moves: List of down-moves with performance data
            measurement_times: Time points analyzed
            
        Returns:
            Summary statistics dictionary
        """
        summary = {
            'total_down_moves': len(enhanced_moves),
            'measurement_times': measurement_times
        }
        
        for measurement_time in measurement_times:
            # Extract returns for this time point
            gross_returns = []
            net_returns = []
            transaction_costs = []
            
            for move in enhanced_moves:
                gross_key = f'gross_return_pct_{measurement_time}s'
                net_key = f'net_return_pct_{measurement_time}s'
                cost_key = f'transaction_cost_pct_{measurement_time}s'
                
                if (gross_key in move and move[gross_key] is not None and
                    net_key in move and move[net_key] is not None):
                    gross_returns.append(move[gross_key])
                    net_returns.append(move[net_key])
                    transaction_costs.append(move[cost_key])
            
            if gross_returns:
                summary[f'{measurement_time}s'] = {
                    'count': len(gross_returns),
                    'gross_return_mean': np.mean(gross_returns),
                    'gross_return_std': np.std(gross_returns),
                    'gross_return_median': np.median(gross_returns),
                    'net_return_mean': np.mean(net_returns),
                    'net_return_std': np.std(net_returns),
                    'net_return_median': np.median(net_returns),
                    'avg_transaction_cost': np.mean(transaction_costs),
                    'positive_returns': sum(1 for r in net_returns if r > 0),
                    'negative_returns': sum(1 for r in net_returns if r < 0),
                    'win_rate': sum(1 for r in net_returns if r > 0) / len(net_returns) * 100
                }
            else:
                summary[f'{measurement_time}s'] = {
                    'count': 0,
                    'message': 'No valid data for this time point'
                }
        
        return summary

def test_performance_calculator():
    """Test the performance calculator with sample data"""
    from pathlib import Path
    
    # Create some mock down-moves for testing
    mock_down_moves = [
        {
            'start_time': pd.Timestamp('2024-08-15 10:00:00'),
            'end_time': pd.Timestamp('2024-08-15 10:01:30'),
            'start_price': 58000.0,
            'min_price': 57100.0,
            'price_drop_pct': 1.55,
            'duration_seconds': 90
        }
    ]
    
    # Load sample trades data
    processor = BinanceDataProcessor()
    trades_file = Path("data/raw/BTCUSDT-trades-2024-08-15.zip")
    
    if not trades_file.exists():
        print("Sample data not found")
        return
    
    print("=== Testing Performance Calculator ===")
    
    trades_df = processor.load_trades_from_zip(trades_file)
    
    # Initialize calculator with typical Binance fees
    calculator = PerformanceCalculator(taker_fee_rate=0.001)  # 0.1% taker fee
    
    # Test spread calculation
    test_time = trades_df['time'].iloc[1000]
    spread_bps = calculator.calculate_spread_cost(trades_df, test_time)
    print(f"Sample spread calculation: {spread_bps:.2f} basis points")
    
    # Test transaction cost calculation
    costs = calculator.calculate_transaction_costs(
        entry_price=58000, exit_price=58500, 
        entry_spread_bps=2.0, exit_spread_bps=2.5
    )
    print(f"Sample transaction costs: {costs['total_cost_pct']:.4f}%")
    
    print("Performance calculator test completed successfully!")

if __name__ == "__main__":
    test_performance_calculator()
