# Binance TAQ Data Analysis Project

## Overview

This project implements a comprehensive analysis system for Binance high-frequency Trade and Quote (TAQ) data, specifically designed to:

1. **Download** the last 3 months of Binance 100ms TAQ data for BTCUSDT and ETHUSDT
2. **Detect** every 5% down-move that occurs in ≤120 seconds
3. **Measure** mid-price recovery at 60s, 90s, and 300s after each down-move
4. **Calculate** net returns after subtracting taker fees (0.04–0.1%) and half the spread at entry/exit

## Project Structure

```
├── data/
│   ├── raw/                    # Downloaded raw data files
│   ├── processed/              # Processed data files
│   └── binance_scripts/        # Official Binance data scripts
├── src/
│   ├── data_acquisition/       # Data download modules
│   ├── analysis/              # Analysis algorithms
│   └── utils/                 # Utility functions
├── results/
│   ├── plots/                 # Generated plots
│   └── reports/               # Analysis reports
└── logs/                      # Log files
```

## Key Features

### 1. High-Frequency Data Processing
- **Microsecond precision**: Processes individual trades with microsecond timestamps
- **Efficient algorithms**: Vectorized operations for handling millions of data points
- **Memory optimization**: Streaming processing for large datasets

### 2. Advanced Price Movement Detection
- **Real-time detection**: Identifies 5% down-moves within 120-second windows
- **Multiple data sources**: Uses both trade-by-trade and 1-second OHLC data
- **Overlap prevention**: Sophisticated logic to avoid double-counting events

### 3. Transaction Cost Analysis
- **Realistic fees**: Incorporates Binance taker fees (0.1%)
- **Spread estimation**: Calculates bid-ask spreads from trade direction
- **Net return calculation**: Provides returns after all transaction costs

### 4. Performance Measurement
- **Multiple timeframes**: Measures recovery at 60s, 90s, and 300s
- **Statistical analysis**: Comprehensive performance metrics
- **Risk assessment**: Win rates, return distributions, and volatility measures

## Data Specifications

### Data Sources
- **Binance Public Data**: Official historical data from data.binance.vision
- **Frequency**: Individual trades (microsecond precision) + 1-second klines
- **Symbols**: BTCUSDT, ETHUSDT
- **Period**: Last 3 months (approximately 90 days)

### Data Volume
- **Trades**: ~2.5 million trades per day per symbol
- **Klines**: 86,400 1-second intervals per day per symbol
- **Total size**: Several GB for 3-month period

## Installation and Setup

### Prerequisites
```bash
pip install -r requirements.txt
```

### Project Setup
```bash
python project_structure.py
```

### Download Sample Data
```bash
python test_download_simple.py
```

### Download Full Dataset
```bash
python download_full_dataset.py
```

## Usage

### Quick Analysis (Sample Data)
```bash
python src/analysis/comprehensive_analysis.py
```

### Full 3-Month Analysis
1. Download the complete dataset:
   ```bash
   python download_full_dataset.py
   ```

2. Run the comprehensive analysis:
   ```bash
   python src/analysis/comprehensive_analysis.py
   ```

### Individual Components

#### Price Movement Detection
```python
from src.analysis.price_movement_detector import PriceMovementDetector

detector = PriceMovementDetector(down_move_threshold=0.05, max_duration_seconds=120)
down_moves = detector.detect_down_moves_from_klines(klines_df)
```

#### Performance Calculation
```python
from src.analysis.performance_calculator import PerformanceCalculator

calculator = PerformanceCalculator(taker_fee_rate=0.001)
enhanced_moves = calculator.calculate_net_returns(down_moves, trades_df)
```

## Analysis Results

### Sample Analysis (August 15, 2024)

**Market Conditions:**
- Relatively stable trading day
- Price range: $56,122.77 to $59,849.38
- No 5% down-moves detected in 120-second windows

**Volatility Statistics:**
- Mean price change per second: 0.0043%
- Maximum 120-second drop: 0.79%
- 99th percentile price change: 0.04%

**Key Insights:**
- 5% drops in 120 seconds are rare events
- Occur primarily during high volatility periods (crashes, major news)
- Normal market conditions show much smaller price movements

### Expected Results for Full Analysis

When 5% down-moves are detected, the system will provide:

1. **Event Statistics**:
   - Frequency of 5% down-moves
   - Average duration and magnitude
   - Time-of-day patterns

2. **Recovery Analysis**:
   - Price recovery at 60s, 90s, 300s intervals
   - Success rates for different holding periods
   - Risk-return profiles

3. **Transaction Cost Impact**:
   - Gross vs. net returns
   - Cost breakdown (fees + spreads)
   - Profitability after realistic costs

## Technical Implementation

### Algorithms

#### Down-Move Detection
```python
# Efficient vectorized detection using numpy
for i in range(len(ohlc_df)):
    window_lows = lows[i:i+max_window_size]
    min_price = np.min(window_lows)
    price_drop = (start_price - min_price) / start_price
    
    if price_drop >= threshold:
        # Record down-move event
```

#### Spread Estimation
```python
# Approximate bid-ask from trade direction
seller_trades = trades[trades['is_buyer_maker'] == True]
buyer_trades = trades[trades['is_buyer_maker'] == False]
bid = seller_trades['price'].mean()
ask = buyer_trades['price'].mean()
spread = ask - bid
```

#### Transaction Cost Calculation
```python
# Total cost = fees + spread costs
entry_fee = entry_price * position_size * taker_fee_rate
exit_fee = exit_price * position_size * taker_fee_rate
spread_cost = (entry_spread + exit_spread) * position_size
total_cost = entry_fee + exit_fee + spread_cost
```

### Performance Optimizations

1. **Vectorized Operations**: NumPy arrays for mathematical operations
2. **Memory Management**: Streaming processing for large files
3. **Efficient Data Structures**: Pandas with optimized data types
4. **Parallel Processing**: Ready for multi-threading implementation

## Limitations and Considerations

### Data Limitations
- **Order book data**: Not available in public data (spread estimation only)
- **Market impact**: Not modeled for large position sizes
- **Slippage**: Not included in transaction cost calculations

### Market Conditions
- **Rare events**: 5% moves in 120s are infrequent in normal conditions
- **Volatility clustering**: Events may cluster during crisis periods
- **Market regime changes**: Results may vary across different market cycles

### Technical Limitations
- **Latency**: Real-world execution latency not modeled
- **Partial fills**: Assumes complete order execution
- **Market hours**: Analysis includes all trading hours

## Future Enhancements

1. **Real-time Processing**: Streaming analysis for live data
2. **Machine Learning**: Predictive models for down-move probability
3. **Risk Management**: Position sizing and stop-loss optimization
4. **Multi-asset Analysis**: Correlation analysis across symbols
5. **Order Book Integration**: Full market depth analysis

## Contributing

This project provides a robust framework for high-frequency cryptocurrency market analysis. The modular design allows for easy extension and customization for different research questions and trading strategies.

## License

MIT License - See LICENSE file for details.

---

**Note**: This analysis is for research purposes only and should not be considered as financial advice. Cryptocurrency trading involves significant risk.
