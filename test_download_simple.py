#!/usr/bin/env python3
"""
Simple test to download Binance data directly
"""

import os
import urllib.request
from pathlib import Path

def test_direct_download():
    """Test downloading directly from Binance data URLs"""
    
    # Create data directory
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # Test URLs for BTCUSDT data
    base_url = "https://data.binance.vision/"
    
    # Try downloading 1s klines for a recent date
    test_date = "2024-08-15"
    symbol = "BTCUSDT"
    
    # Klines URL format: data/spot/daily/klines/BTCUSDT/1s/BTCUSDT-1s-2024-08-15.zip
    klines_url = f"{base_url}data/spot/daily/klines/{symbol}/1s/{symbol}-1s-{test_date}.zip"
    klines_file = data_dir / f"{symbol}-1s-{test_date}.zip"
    
    print(f"Testing download from: {klines_url}")
    
    try:
        print("Downloading klines data...")
        urllib.request.urlretrieve(klines_url, klines_file)
        print(f"✓ Successfully downloaded: {klines_file}")
        print(f"File size: {klines_file.stat().st_size} bytes")
    except Exception as e:
        print(f"✗ Klines download failed: {e}")
    
    # Try trades data
    trades_url = f"{base_url}data/spot/daily/trades/{symbol}/{symbol}-trades-{test_date}.zip"
    trades_file = data_dir / f"{symbol}-trades-{test_date}.zip"
    
    print(f"\nTesting download from: {trades_url}")
    
    try:
        print("Downloading trades data...")
        urllib.request.urlretrieve(trades_url, trades_file)
        print(f"✓ Successfully downloaded: {trades_file}")
        print(f"File size: {trades_file.stat().st_size} bytes")
    except Exception as e:
        print(f"✗ Trades download failed: {e}")

if __name__ == "__main__":
    test_direct_download()
