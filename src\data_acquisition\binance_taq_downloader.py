#!/usr/bin/env python3
"""
Binance TAQ Data Downloader for BTCUSDT and ETHUSDT
Downloads last 3 months of high-frequency trade and kline data
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

# Add the binance scripts to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../data/binance_scripts/python'))

from utility import download_file, get_path
from enums import TRADING_TYPE

class BinanceTAQDownloader:
    def __init__(self, data_folder="data/raw"):
        self.data_folder = data_folder
        self.symbols = ["BTCUSDT", "ETHUSDT"]
        self.trading_type = "spot"
        
        # Calculate last 3 months
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)  # Approximately 3 months
        
        self.start_date = start_date.strftime("%Y-%m-%d")
        self.end_date = end_date.strftime("%Y-%m-%d")
        
        print(f"Downloading data from {self.start_date} to {self.end_date}")
        
        # Create data folder
        Path(self.data_folder).mkdir(parents=True, exist_ok=True)
    
    def get_date_range(self):
        """Generate list of dates for the last 3 months"""
        start = datetime.strptime(self.start_date, "%Y-%m-%d")
        end = datetime.strptime(self.end_date, "%Y-%m-%d")
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime("%Y-%m-%d"))
            current += timedelta(days=1)
        
        return dates
    
    def download_trades_data(self):
        """Download trade data for both symbols"""
        print("\n=== Downloading Trade Data ===")
        dates = self.get_date_range()
        
        for symbol in self.symbols:
            print(f"\nDownloading trades for {symbol}")
            
            for date in dates:
                try:
                    path = get_path(self.trading_type, "trades", "daily", symbol)
                    file_name = f"{symbol.upper()}-trades-{date}.zip"
                    
                    print(f"Downloading {file_name}")
                    download_file(path, file_name, None, self.data_folder)
                    
                    # Also download checksum
                    checksum_file_name = f"{symbol.upper()}-trades-{date}.zip.CHECKSUM"
                    download_file(path, checksum_file_name, None, self.data_folder)
                    
                except Exception as e:
                    print(f"Error downloading {file_name}: {e}")
                    continue
    
    def download_klines_data(self):
        """Download kline data at highest frequency (1s intervals)"""
        print("\n=== Downloading Kline Data (1s intervals) ===")
        dates = self.get_date_range()
        interval = "1s"  # Highest frequency available
        
        for symbol in self.symbols:
            print(f"\nDownloading 1s klines for {symbol}")
            
            for date in dates:
                try:
                    path = get_path(self.trading_type, "klines", "daily", symbol, interval)
                    file_name = f"{symbol.upper()}-{interval}-{date}.zip"
                    
                    print(f"Downloading {file_name}")
                    download_file(path, file_name, None, self.data_folder)
                    
                    # Also download checksum
                    checksum_file_name = f"{symbol.upper()}-{interval}-{date}.zip.CHECKSUM"
                    download_file(path, checksum_file_name, None, self.data_folder)
                    
                except Exception as e:
                    print(f"Error downloading {file_name}: {e}")
                    continue
    
    def download_all_data(self):
        """Download both trades and klines data"""
        print("Starting Binance TAQ data download...")
        print(f"Symbols: {self.symbols}")
        print(f"Date range: {self.start_date} to {self.end_date}")
        print(f"Data folder: {self.data_folder}")
        
        # Download trades (actual transactions)
        self.download_trades_data()
        
        # Download 1s klines (OHLCV data at 1-second intervals)
        self.download_klines_data()
        
        print("\n=== Download Complete ===")
        print(f"Data saved to: {self.data_folder}")

if __name__ == "__main__":
    downloader = BinanceTAQDownloader()
    downloader.download_all_data()
